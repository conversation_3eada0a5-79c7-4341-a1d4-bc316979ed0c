#!/usr/bin/env python3
# -*- coding: UTF-8 -*-

"""
测试目标选择系统的脚本
"""

import numpy as np
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from agent_ppo.feature.preprocessor import Preprocessor

def create_mock_frame_state():
    """创建模拟的frame_state数据"""
    
    # 模拟英雄数据
    hero = {
        "pos": {"x": 64, "z": 64},
        "talent": {"cooldown": 0}
    }
    
    # 模拟organs数据 - 包含宝箱和buff
    organs = [
        # 宝箱1 - 可获取
        {
            "sub_type": 1,
            "config_id": 1,
            "status": 1,
            "pos": {"x": 70, "z": 70},
            "relative_pos": {"l2_distance": "Small", "direction": "NorthEast"}
        },
        # 宝箱2 - 不可获取
        {
            "sub_type": 1,
            "config_id": 2,
            "status": 0,
            "pos": {"x": 50, "z": 50},
            "relative_pos": {"l2_distance": "Medium", "direction": "SouthWest"}
        },
        # buff - 可获取
        {
            "sub_type": 2,
            "config_id": 0,
            "status": 1,
            "pos": {"x": 60, "z": 68},
            "relative_pos": {"l2_distance": "VerySmall", "direction": "West"}
        },
        # 终点 - 视野外
        {
            "sub_type": 4,
            "config_id": 22,
            "status": -1,
            "pos": {"x": 100, "z": 100},
            "relative_pos": {"l2_distance": "Large", "direction": "NorthEast"}
        }
    ]
    
    # 模拟map_info
    map_info = []
    for r in range(11):
        row_data = {"values": [1] * 11}  # 1表示可通行
        # 在某些位置放置宝箱和buff
        if r == 5 and 5 < 11:
            row_data["values"][6] = 4  # 宝箱
        if r == 4 and 4 < 11:
            row_data["values"][4] = 6  # buff
        map_info.append(row_data)
    
    obs = {
        "frame_state": {
            "step_no": 100,
            "heroes": [hero],
            "organs": organs
        },
        "map_info": map_info
    }
    
    # 模拟game_info
    game_info = {
        "pos": {"x": 64, "z": 64},
        "end_pos": {"x": 100, "z": 100},
        "frame_state": {
            "organs": [
                {
                    "sub_type": 1,
                    "config_id": 1,
                    "status": 1,
                    "pos": {"x": 70, "z": 70}
                }
            ]
        },
        "score_info": {"buff_count": 2}
    }
    
    return (obs, game_info)

def test_target_selection():
    """测试目标选择系统"""
    print("开始测试目标选择系统...")
    
    # 创建preprocessor实例
    preprocessor = Preprocessor()
    
    # 创建模拟数据
    frame_state = create_mock_frame_state()
    last_action = -1
    
    try:
        # 处理第一帧
        print("\n=== 处理第一帧 ===")
        result = preprocessor.process(frame_state, last_action)
        vector_feature, map_features, legal_action, reward = result
        
        print(f"向量特征维度: {vector_feature.shape}")
        print(f"地图特征维度: {map_features.shape}")
        print(f"合法动作数量: {sum(legal_action)}")
        print(f"奖励: {reward}")

        # 检查目标选择
        print(f"\n当前目标: {preprocessor.target}")
        print(f"目标特征维度: {preprocessor.feature_target_pos.shape}")
        print(f"多目标特征形状: {preprocessor.multi_target_features.shape}")

        # 验证特征维度
        expected_vector_dim = 2 + 6 + 15 + 6 + 18  # 47
        actual_vector_dim = vector_feature.shape[0]
        print(f"\n特征维度验证:")
        print(f"期望维度: {expected_vector_dim}")
        print(f"实际维度: {actual_vector_dim}")
        print(f"维度匹配: {'✅' if expected_vector_dim == actual_vector_dim else '❌'}")
        
        # 检查历史记录
        print(f"\n历史宝箱记录: {preprocessor.historical_treasures}")
        print(f"历史buff记录: {preprocessor.historical_buffs}")
        
        # 模拟移动到新位置
        print("\n=== 模拟移动到新位置 ===")
        # 修改当前位置
        frame_state[0]["frame_state"]["heroes"][0]["pos"] = {"x": 68, "z": 68}
        
        # 处理第二帧
        result2 = preprocessor.process(frame_state, 0)  # 假设执行了动作0
        vector_feature2, map_features2, legal_action2, reward2 = result2
        
        print(f"新位置目标: {preprocessor.target}")
        print(f"新位置奖励: {reward2}")
        
        print("\n测试完成！目标选择系统工作正常。")
        return True
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_target_selection()
    if success:
        print("\n✅ 所有测试通过！")
    else:
        print("\n❌ 测试失败！")
        sys.exit(1)
