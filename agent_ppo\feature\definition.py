#!/usr/bin/env python3
# -*- coding: UTF-8 -*-
###########################################################################
# Copyright © 1998 - 2025 Tencent. All Rights Reserved.
###########################################################################
"""
Author: Tencent AI Arena Authors
"""

from agent_ppo.conf.conf import Config
from kaiwu_agent.utils.common_func import create_cls, attached
import numpy as np
from typing import List
# The create_cls function is used to dynamically create a class.
# The first parameter of the function is the type name, and the remaining parameters are the attributes of the class.
# The default value of the attribute should be set to None.
# create_cls函数用于动态创建一个类，函数第一个参数为类型名称，剩余参数为类的属性，属性默认值应设为None
ObsData = create_cls(
    "ObsData",
    feature=None,
    legal_action=None,
    reward=None,
)


ActData = create_cls(
    "ActData",
    probs=None,
    value=None,
    target=None,
    predict=None,
    action=None,
    prob=None,
)

SampleData = create_cls("SampleData", npdata=None)

RelativeDistance = {
    "RELATIVE_DISTANCE_NONE": 0,
    "VerySmall": 1,
    "Small": 2,
    "Medium": 3,
    "Large": 4,
    "VeryLarge": 5,
}


RelativeDirection = {
    "East": 1,
    "NorthEast": 2,
    "North": 3,
    "NorthWest": 4,
    "West": 5,
    "SouthWest": 6,
    "South": 7,
    "SouthEast": 8,
}

DirectionAngles = {
    1: 0,
    2: 45,
    3: 90,
    4: 135,
    5: 180,
    6: 225,
    7: 270,
    8: 315,
}
def get_locked_target_reward(
    current_distances: List[float],
    previous_distances: List[float],
    nearest_treasure_index: int,
    prev_nearest_treasure_index: int,
) -> float:

    REWARD_COLLECTED = 5.0
    REWARD_SCALING_FACTOR = 0.1  
    INVALID_DISTANCE_VALUE = 999 


    if current_distances.count(-1) > previous_distances.count(-1):

        return REWARD_COLLECTED + 2.5 * current_distances.count(-1)


    if prev_nearest_treasure_index < 0 or prev_nearest_treasure_index >= len(previous_distances):

        return 0.0

    prev_dist_to_locked_target = previous_distances[prev_nearest_treasure_index]
    current_dist_to_locked_target = current_distances[prev_nearest_treasure_index]


    if prev_dist_to_locked_target <= 0 or prev_dist_to_locked_target == INVALID_DISTANCE_VALUE or \
       current_dist_to_locked_target <= 0 or current_dist_to_locked_target == INVALID_DISTANCE_VALUE:
        return 0.0


    distance_delta = prev_dist_to_locked_target - current_dist_to_locked_target
    reward = distance_delta * REWARD_SCALING_FACTOR

    #print(f"treasure :", reward)
    return reward

def reward_process(prev_end_dist, 
                   cur_end_dist, 
                   history_dist, 
                   memory_map=0.0, 
                   treasure_distance_list=None, 
                   prev_treasure_distance_list=None, 
                   prev_real_end_dist=None, 
                   real_end_dist=None, 
                   step_no=None, 
                   bad_move_penalty=0.0,
                   is_use_flash=False,
                   flash_hit_wall_penalty = 0.0,
                   cur_buff_count=0,
                   prev_buff_count=0,
                   nearest_treasure_index=None,
                   prev_nearest_treasure_index=None,
                   ):
    # 步数奖励
    step_reward = -0.001
    potential_reward = 0.0
    memory_reward = 0.0
    treasure_reward = 0.0
    dist_reward  = 0.0
    repeat_weight = np.array([
        [0, 0, 0, 0, 0],
        [0, 0.05, 0.08, 0.05, 0],
        [0, 0.08, 0.1, 0.08, 0],
        [0, 0.05, 0.08, 0.05, 0],
        [0, 0, 0, 0, 0],
    ])


    step_reward = -0.01
    potential_reward = 0.0
    memory_reward = 0.0
    treasure_reward = 0.0
    memory_value = memory_map[5,5]
    if step_no < 750 and treasure_distance_list is not None and treasure_distance_list.count(-1) != 8:
        # memory reward - 重複訪問懲罰
        memory_reward = 0.0
        if memory_value > 0.21:
            memory_reward = -memory_value

        # treasure reward
        # 寶藏獎勵
        if prev_treasure_distance_list is not None and len(prev_treasure_distance_list) > 0:
            treasure_reward = get_locked_target_reward(treasure_distance_list, prev_treasure_distance_list, nearest_treasure_index, prev_nearest_treasure_index)
    else:
        # 勢能獎勵塑形
        if prev_real_end_dist is not None and real_end_dist is not None:
            potential_reward = (prev_real_end_dist - real_end_dist)*0.01
            
    
    # distance reward
    # 距离奖励
    dist_reward = min(0.001, 0.05 * history_dist)
    
    
    total_reward = step_reward + dist_reward + potential_reward + memory_reward + treasure_reward + flash_hit_wall_penalty
    
    
    return [total_reward]


class SampleManager:
    def __init__(
        self,
        gamma=0.99,
        tdlambda=0.95,
    ):
        self.gamma = Config.GAMMA
        self.tdlambda = Config.TDLAMBDA

        self.feature = []
        self.probs = []
        self.actions = []
        self.reward = []
        self.value = []
        self.adv = []
        self.tdlamret = []
        self.legal_action = []
        self.count = 0
        self.samples = []

    def add(self, feature, legal_action, prob, action, value, reward):
        self.feature.append(feature)
        self.legal_action.append(legal_action)
        self.probs.append(prob)
        self.actions.append(action)
        self.value.append(value)
        self.reward.append(reward)
        self.adv.append(np.zeros_like(value))
        self.tdlamret.append(np.zeros_like(value))
        self.count += 1

    def add_last_reward(self, reward):
        self.reward.append(reward)
        self.value.append(np.zeros_like(reward))

    def update_sample_info(self):
        last_gae = 0
        for i in range(self.count - 1, -1, -1):
            reward = self.reward[i + 1]
            next_val = self.value[i + 1]
            val = self.value[i]
            delta = reward + next_val * self.gamma - val
            last_gae = delta + self.gamma * self.tdlambda * last_gae
            self.adv[i] = last_gae
            self.tdlamret[i] = last_gae + val

    def sample_process(self, feature, legal_action, prob, action, value, reward):
        self.add(feature, legal_action, prob, action, value, reward)

    def process_last_frame(self, reward):
        self.add_last_reward(reward)
        # 发送前的后向传递更新
        # Backward pass updates before sending
        self.update_sample_info()
        self.samples = self._get_game_data()

    def get_game_data(self):
        ret = self.samples
        self.samples = []
        return ret

    def _get_game_data(self):
        feature = np.array(self.feature).transpose()
        probs = np.array(self.probs).transpose()
        actions = np.array(self.actions).transpose()
        reward = np.array(self.reward[:-1]).transpose()
        value = np.array(self.value[:-1]).transpose()
        legal_action = np.array(self.legal_action).transpose()
        adv = np.array(self.adv).transpose()
        tdlamret = np.array(self.tdlamret).transpose()

        data = np.concatenate([feature, reward, value, tdlamret, adv, actions, probs, legal_action]).transpose()

        samples = []
        for i in range(0, self.count):
            samples.append(SampleData(npdata=data[i].astype(np.float32)))

        return samples


@attached
def SampleData2NumpyData(g_data):
    return g_data.npdata


@attached
def NumpyData2SampleData(s_data):
    return SampleData(npdata=s_data)
