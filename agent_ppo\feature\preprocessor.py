#!/usr/bin/env python3
# -*- coding: UTF-8 -*-
###########################################################################
# Copyright © 1998 - 2025 Tencent. All Rights Reserved.
###########################################################################

"""
Author: Tencent AI Arena Authors

"""

import numpy as np
import math
from agent_ppo.feature.definition import RelativeDistance, RelativeDirection, DirectionAngles, reward_process

# ----------------  utils  ----------------
def norm(v, max_v, min_v=0):
    v = np.maximum(np.minimum(max_v, v), min_v)
    return (v - min_v) / (max_v - min_v)

def angle_to_vec(direction: str) -> tuple[float, float]:
    theta = math.radians(DirectionAngles[RelativeDirection[direction]])
    return math.cos(theta), math.sin(theta)

def dist_to_scalar(l2_dist_str: str) -> float:
    mapping = {
        "VerySmall": 2.5, "Small": 7.5, "Medium": 15,
        "Large": 25, "VeryLarge": 40
    }
    return mapping[l2_dist_str] / 40.0  # 歸一化到 0-1
# -----------------------------------------

class Preprocessor:
    def __init__(self) -> None:
        # 从8修改成16，代表8个移动维度，8个闪现维度
        self.move_action_num = 16
        self.reset()

    def reset(self):
        self.step_no = 0
        self.cur_pos = (0, 0)
        self.cur_pos_norm = np.array((0, 0))
        self.end_pos = None
        self.is_end_pos_found = False
        self.end_pos_dir = None
        self.end_pos_dis = None
        self.history_pos = []
        self.bad_move_ids = set()
        # 闪现是否可用，初始化为True
        self.is_flash_usable = True
        # 初始化记忆矩阵
        self.global_memory_map = np.zeros((128,128), dtype=np.float32)
        self.local_memory_map = np.zeros((11,11), dtype=np.float32)

        # 初始化organs特征系统
        self._reset_organs_features()
        self.prev_end_dist = 0.0

        self.prev_end_dist = 0.0  # 新增: 記錄上一步的end_dist
        # 新增: 記錄上一步的buff_count
        self.prev_buff_count = 0
        self.cur_buff_count = 0
        # 初始化寶箱距離列表為999*13
        self.treasure_distance_list = [999.0] * 13
        self.prev_treasure_distance_list = [999.0] * 13
        # 新增: 記錄最近距離寶箱的index
        self.nearest_treasure_index = -1
        self.prev_nearest_treasure_index = -1
        # 初始化終點位置
        self.real_end_pos = None
        self.real_end_dist = 0.0
        self.prev_real_end_dist = 0.0
        self.bad_move_penalty = 0.0
        self.is_use_flash = False
        self.flash_hit_wall_penalty = 0.0

    def _reset_organs_features(self):
        """重置organs原始特征"""
        # 简化版: 15個 organ, 每個 1 維 (只保留status，用数值表示)
        self.organs_features = np.zeros((15, 1), dtype=np.float32)

    def _generate_local_flags(self, organs, map_info):
        """
        根据英雄当前位置和视野内的物件生成11x11的局部地图标志
        使用四个独立的地图矩阵：treasure_map、end_map、obstacle_map、buff_map
        """
        # 初始化四个11x11的地图矩阵
        treasure_map = np.zeros((11, 11), dtype=np.float32)
        end_map = np.zeros((11, 11), dtype=np.float32)
        obstacle_map = np.zeros((11, 11), dtype=np.float32)
        buff_map = np.zeros((11, 11), dtype=np.float32)

        # 遍历 map_info 并填充矩阵
        for r, row_data in enumerate(map_info):
            for c, value in enumerate(row_data['values']):
                # 宝箱
                if value == 4:
                    treasure_map[r, c] = 1.0
                    print("map_info treasure")
                # 终点
                elif value == 3:
                    end_map[r, c] = 1.0
                    print("map_info end")
                # 障碍物
                elif value == 0:
                    obstacle_map[r, c] = 1.0
                # 加速增益
                elif value == 6:
                    buff_map[r, c] = 1.0
                    print("map_info buff")

        # 将矩阵转换为flatten格式以保持与现有代码的兼容性
        self.treasure_flag = treasure_map.flatten()
        self.end_flag = end_map.flatten()
        self.obstacle_flag = obstacle_map.flatten()
        self.buff_flag = buff_map.flatten()

    def memory_update(self, cur_pos):
        """
        记忆矩阵更新
        """
        # 全局记忆矩阵
        x,z = cur_pos
        z = 127 - z
        current_value = self.global_memory_map[z, x]
        self.global_memory_map[z, x] = current_value + 0.1

        # 局部记忆矩阵
        # 计算在全局地图上的源区域边界
        src_top = max(0, z - 5)
        src_bottom = min(128, z + 6)
        src_left = max(0, x - 5)
        src_right = min(128, x + 6)

        # 计算在局部地图上的目标区域边界
        dst_top = src_top - (z - 5)
        dst_bottom = src_bottom - (z - 5)
        dst_left = src_left - (x - 5)
        dst_right = src_right - (x - 5)

        # 从全局地图复制有效区域到局部地图
        self.local_memory_map[dst_top:dst_bottom, dst_left:dst_right] = self.global_memory_map[src_top:src_bottom, src_left:src_right]
        self.memory_flag = self.local_memory_map.flatten()

    def _get_pos_feature(self, found, cur_pos, target_pos):
        relative_pos = tuple(y - x for x, y in zip(cur_pos, target_pos))
        dist = np.linalg.norm(relative_pos)
        target_pos_norm = norm(target_pos, 128, -128)
        feature = np.array(
            (
                found,
                norm(relative_pos[0] / max(dist, 1e-4), 1, -1),
                norm(relative_pos[1] / max(dist, 1e-4), 1, -1),
                target_pos_norm[0],
                target_pos_norm[1],
                norm(dist, 1.41 * 128),
            ),
        )
        return feature

    def pb2struct(self, frame_state, last_action):
        obs, _ = frame_state
        self.step_no = obs["frame_state"]["step_no"]

        hero = obs["frame_state"]["heroes"][0]
        map_info = obs["map_info"]  # 解析map_info，暂未使用


        self.is_flash_usable = (hero['talent']['cooldown'] == 0)

        # 获取当前位置
        self.cur_pos = (hero["pos"]["x"], hero["pos"]["z"])

        # 保存上一個step的寶箱距離列表
        self.prev_treasure_distance_list = self.treasure_distance_list.copy() if hasattr(self, 'treasure_distance_list') else [999.0] * 13
        # 保存上一個step的buff_count
        self.prev_buff_count = self.cur_buff_count
        # 保存上一個step的最近寶箱index
        self.prev_nearest_treasure_index = self.nearest_treasure_index if hasattr(self, 'nearest_treasure_index') else -1
        # 創建長度為13的數組來存儲寶箱距離
        treasure_distance_list = [999.0] * 13
        
        # 計算寶箱距離列表
        if _ is not None:
            # 保存上一個step的 real_end_dist
            self.prev_real_end_dist = self.real_end_dist
            # 解析當前end_pos
            if 'game_info' in _ and 'end_pos' in _['game_info']:
                end_pos_dict = _['game_info']['end_pos']
                self.real_end_pos = (end_pos_dict.get('x', 0), end_pos_dict.get('z', 0))
            # 計算 real_end_dist (使用曼哈頓距離)
            if self.real_end_pos is not None:
                agent_x = _['game_info']['pos']['x']
                agent_z = _['game_info']['pos']['z']
                self.real_end_dist = abs(agent_x - self.real_end_pos[0]) + abs(agent_z - self.real_end_pos[1])
            else:
                self.real_end_dist = 0.0
            # 計算寶箱距離 (使用曼哈頓距離)
            agent_x = _['game_info']['pos']['x']
            agent_z = _['game_info']['pos']['z']
            
            # 記錄有效寶箱的距離和index，用於找到最近的寶箱
            valid_treasures = []
            
            for organ in _['frame_state']['organs']:
                if organ['sub_type'] == 1:
                    config_id = organ['config_id']
                    if organ['status'] == 0:
                        treasure_distance_list[config_id - 1] = -1
                        continue
                    treasure_x = organ['pos']['x']
                    treasure_z = organ['pos']['z']
                    distance = abs(treasure_x - agent_x) + abs(treasure_z - agent_z)
                    treasure_distance_list[config_id - 1] = distance  # config_id從1開始，數組索引從0開始
                    valid_treasures.append((distance, config_id - 1))
            
            # 找到最近距離的寶箱index
            if valid_treasures:
                min_distance, min_index = min(valid_treasures, key=lambda x: x[0])
                self.nearest_treasure_index = min_index
            else:
                self.nearest_treasure_index = -1
        else:
            # 如果沒有game_info，設置為默認值
            self.nearest_treasure_index = -1
                
        self.treasure_distance_list = treasure_distance_list
        
        # 获取当前buff_count
        if _ is not None and 'score_info' in frame_state[0]:
            self.cur_buff_count = frame_state[0]['score_info'].get('buff_count', 0)
        else:
            self.cur_buff_count = 0

        # 生成基于英雄视野的11x11局部地图标志
        self._generate_local_flags(obs["frame_state"]["organs"], map_info)



        # 更新记忆矩阵
        self.memory_update(self.cur_pos)

        # End position
        # 终点位置
        for organ in obs["frame_state"]["organs"]:
            if organ["sub_type"] == 4:
                end_pos_dis = RelativeDistance[organ["relative_pos"]["l2_distance"]]
                end_pos_dir = RelativeDirection[organ["relative_pos"]["direction"]]
                if organ["status"] != -1:
                    self.end_pos = (organ["pos"]["x"], organ["pos"]["z"])
                    self.is_end_pos_found = True
                # if end_pos is not found, use relative position to predict end_pos
                # 如果终点位置未找到，使用相对位置预测终点位置
                elif (not self.is_end_pos_found) and (
                    self.end_pos is None
                    or self.step_no % 100 == 0
                    or self.end_pos_dir != end_pos_dir
                    or self.end_pos_dis != end_pos_dis
                ):
                    distance = end_pos_dis * 20
                    theta = DirectionAngles[end_pos_dir]
                    delta_x = distance * math.cos(math.radians(theta))
                    delta_z = distance * math.sin(math.radians(theta))

                    self.end_pos = (
                        max(0, min(128, round(self.cur_pos[0] + delta_x))),
                        max(0, min(128, round(self.cur_pos[1] + delta_z))),
                    )

                    self.end_pos_dir = end_pos_dir
                    self.end_pos_dis = end_pos_dis

        '''
        message RealmOrgan {
        int32 sub_type = 1; // 物件类型，1代表宝箱, 2代表加速buff,3代表起点,4代表终点
        int32 config_id = 2; // 物件id 0代表buff，1~13代表宝箱 21代表起点, 22代表终点
        int32 status = 3; // 0表示不可获取，1表示可获取, -1表示视野外
        Position pos = 4; // 物件位置坐标
        int32 cooldown = 5;                // 物件剩余冷却时间
        RelativePosition relative_pos = 6; // 物件相对位置
        }
        '''

        # 重置organs特征矩阵
        self.organs_features.fill(0)  # 全部初始化为0

        # 遍历organs数据，进行简化的特征编码（只保留status）
        for organ in obs["frame_state"]["organs"]:
            config_id = organ["config_id"]
            sub_type = organ["sub_type"]
            status = organ["status"]

            # 确定存储索引
            if sub_type == 4:  # 终点
                organ_idx = 14
            elif 0 <= config_id < 14:  # 宝箱和buff
                organ_idx = config_id
            else:
                continue  # 跳过无效的organ

            # 構建簡化特徵向量 (1維，只保留status，用数值表示)
            feature_vector = np.zeros(1, dtype=np.float32)

            # status编码 (1维): 直接使用status值
            # status = -1: 视野外, status = 0: 不可获取, status = 1: 可获取
            if status == -1 or status == 0:
                feature_vector[0] = 0.0
            else:
                feature_vector[0] = 1.0

            # 存储编码后的特征
            self.organs_features[organ_idx] = feature_vector




        # History position
        # 历史位置
        self.history_pos.append(self.cur_pos)
        if len(self.history_pos) > 10:
            self.history_pos.pop(0)

            

        self.last_pos_norm = self.cur_pos_norm
        self.cur_pos_norm = norm(self.cur_pos, 128, -128)
        self.feature_end_pos = self._get_pos_feature(self.is_end_pos_found, self.cur_pos, self.end_pos)

        # History position feature
        # 历史位置特征
        self.feature_history_pos = self._get_pos_feature(1, self.cur_pos, self.history_pos[0])

        self.move_usable = True
        self.last_action = last_action

    def process(self, frame_state, last_action):
        self.pb2struct(frame_state, last_action)
        #only for reward, eval is None frame_state[1] 


        # 1. 準備【純淨的】向量特徵 (Purified Vector Feature)
        vector_feature = np.concatenate([
            self.cur_pos_norm,                     # 2維
            self.feature_history_pos,              # 6維
            self.organs_features.flatten(),        # 15維 (15*1)
        ])  # 新的總維度: 23

        # 2. 準備【空間結構的】地圖特徵 (Spatial Map Feature)
        map_features = np.stack([
            self.treasure_flag.reshape(11, 11),
            self.obstacle_flag.reshape(11, 11),
            self.buff_flag.reshape(11, 11),
            self.end_flag.reshape(11, 11),
            self.local_memory_map
        ], axis=0)  # 輸出形狀: (5, 11, 11)

        # 3. 準備合法動作
        legal_action = self.get_legal_action()

        # 4. 準備獎勵
        # 計算當前位置的記憶地圖值（用於重複訪問懲罰）
        center_x, center_y = 5, 5  # 11x11地圖的中心點
        current_memory_value = self.local_memory_map[center_x, center_y]

        prev_end_dist = self.prev_end_dist
        cur_end_dist = self.feature_end_pos[-1]
        # 获取当前和上一步的buff_count
        cur_buff_count = self.cur_buff_count
        prev_buff_count = self.prev_buff_count

        if self.last_action >= 8 and self.last_action < 16:
            self.is_use_flash = True
        else:
            self.is_use_flash = False

        if self.is_use_flash:
            if len(self.history_pos) >= 2:
                prev_pos = self.history_pos[-2]
                current_pos = self.cur_pos

                delta_x = current_pos[0] - prev_pos[0]
                delta_y = current_pos[1] - prev_pos[1]
                actual_distance = math.hypot(delta_x, delta_y)

                if actual_distance < 15: 
                    self.flash_hit_wall_penalty = -5
        else:
            self.flash_hit_wall_penalty = 0


        reward = reward_process(
            prev_end_dist, cur_end_dist, self.feature_history_pos[-1],
            self.local_memory_map,
            self.treasure_distance_list, self.prev_treasure_distance_list,
            self.prev_real_end_dist, self.real_end_dist,
            self.step_no, 
            self.bad_move_penalty,
            self.is_use_flash,
            self.flash_hit_wall_penalty,
            cur_buff_count,
            prev_buff_count,
            self.nearest_treasure_index,
            self.prev_nearest_treasure_index,
        )
        self.prev_end_dist = cur_end_dist  # 更新prev_end_dist

        # 返回清晰分離的四個部分
        return (
            vector_feature,  # np.array, shape (23,)
            map_features,    # np.array, shape (5, 11, 11)
            legal_action,
            reward,
        )

    def get_legal_action(self):
        if (
            self.last_action > -1
            and abs(self.cur_pos_norm[0] - self.last_pos_norm[0]) < 1e-4
            and abs(self.cur_pos_norm[1] - self.last_pos_norm[1]) < 1e-4
        ):
            self.bad_move_ids.add(self.last_action)
            self.bad_move_penalty = -0.01
        else:
            self.bad_move_ids.clear()
            self.bad_move_penalty = 0.0
            
        # 2. 以基礎移動能力初始化合法動作列表
        legal_action = [self.move_usable] * self.move_action_num

        # 3. 疊加閃現冷卻的限制
        if not self.is_flash_usable:
            # 如果閃現不可用，將對應的動作（8到15）設為 False
            for i in range(8, 16):
                legal_action[i] = False

        # 4. 疊加無效移動的限制
        for move_id in self.bad_move_ids:
            if 0 <= move_id < self.move_action_num:
                legal_action[move_id] = False

        # 如果所有動作都被禁用了，重置 bad_move_ids 允許智能體嘗試脫困
        if not any(legal_action):
            self.bad_move_ids.clear()
            # 重新生成一次，這次不再考慮 bad_move_ids
            legal_action = [self.move_usable] * self.move_action_num
            if not self.is_flash_usable:
                for i in range(8, 16):
                    legal_action[i] = False
            
        return legal_action



